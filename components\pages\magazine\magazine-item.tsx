import { But<PERSON> } from "@/components/ui/button";
import { ArrowDownToLine, Eye } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface MagazineItemProps {
   year: number;
   coverImage: string;
   description: string;
   title: string;
}

export default function MagazineItem({
   year,
   coverImage,
   description,
   title,
}: MagazineItemProps) {
   return (
      <article className="group mb-12 overflow-hidden bg-white shadow-xl transition-all duration-300">
         {/* Magazine Cover Image */}
         <div className="relative aspect-[16/7] w-full overflow-hidden">
            <Image
               src={coverImage}
               alt={`Mlist Magazine ${year} cover`}
               fill
               className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
            {/* Enhanced Cover Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

            {/* Year Badge */}
            <div className="absolute top-6 left-6">
               <div className="rounded-full bg-white/90 px-4 py-2 shadow-lg backdrop-blur-sm">
                  <p className="text-sm font-bold text-gray-900">{year}</p>
               </div>
            </div>

            {/* Featured Badge */}
            <div className="absolute top-6 right-6">
               <div className="rounded-full bg-black/80 px-3 py-1 backdrop-blur-sm">
                  <p className="text-xs font-medium text-white">
                     FEATURED EDITION
                  </p>
               </div>
            </div>

            {/* Bottom Overlay Content */}
            <div className="absolute right-6 bottom-6 left-6">
               <h2 className="mb-2 line-clamp-2 text-2xl font-bold text-white md:text-3xl">
                  {title}
               </h2>
               <p className="line-clamp-2 text-sm text-white/90 md:text-base">
                  {description}
               </p>
            </div>
         </div>

         {/* Enhanced Content Section */}
         <div className="space-y-6 p-6">
            {/* Action Buttons */}
            <div className="flex flex-col gap-3 sm:flex-row">
               <Button
                  className="border-primary w-fit rounded-xl border px-5 py-5 text-base font-medium shadow-lg transition-all duration-300 hover:shadow-xl"
                  aria-label={`Download Mlist Magazine ${year}`}
               >
                  <Link
                     href="#"
                     className="flex items-center justify-center gap-3"
                  >
                     <ArrowDownToLine size={18} />
                     Download PDF
                  </Link>
               </Button>

               <Button
                  variant="outline"
                  className="w-fit rounded-xl border-1 px-5 py-5 text-base font-medium shadow-lg transition-all duration-300 hover:bg-gray-50"
                  aria-label={`Preview Mlist Magazine ${year}`}
               >
                  <Link
                     href="#"
                     className="flex items-center justify-center gap-3"
                  >
                     <Eye size={18} />
                     Preview
                  </Link>
               </Button>
            </div>
         </div>
      </article>
   );
}

"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useCallback, useEffect, useRef, useState } from "react";

// Configuration for the carousel
const CAROUSEL_CONFIG = {
   autoPlayInterval: 2000, // 2 seconds
   transitionDuration: 800, // 0.8 seconds
};

// Background images for the carousel
const HERO_BACKGROUNDS = [
   {
      src: "/images/hero/hero-bg.png",
      alt: "Creative professional",
   },
   {
      src: "/images/hero/list-bg.png",
      alt: "Creative workspace",
   },
   {
      src: "/images/hero/hero-bg.png",
      alt: "Creative professional",
   },
   // Add more background images here as needed
   // {
   //    src: "/images/hero/hero-bg-3.png",
   //    alt: "Creative studio",
   // },
];

type TransitionType = "fade" | "slide";

interface HeroCarouselProps {
   transitionType?: TransitionType;
}

export default function Hero({ transitionType = "slide" }: HeroCarouselProps) {
   const [currentImageIndex, setCurrentImageIndex] = useState(0);
   const [isPlaying, setIsPlaying] = useState(true);
   const [isTransitioning, setIsTransitioning] = useState(false);
   const carouselRef = useRef<HTMLDivElement>(null);
   const isAnimatingRef = useRef(false);

   // DOM manipulation for infinite sliding with smooth transitions
   const moveSlideElement = useCallback(
      (direction: "next" | "prev") => {
         if (
            !carouselRef.current ||
            isAnimatingRef.current ||
            transitionType !== "slide"
         )
            return;

         const container = carouselRef.current;
         const slides = container.children;

         if (slides.length === 0) return;

         isAnimatingRef.current = true;

         if (direction === "next") {
            // For next: Start the slide transition first, then move DOM element
            container.style.transition = `transform ${CAROUSEL_CONFIG.transitionDuration}ms ease-in-out`;
            container.style.transform = "translateX(-100%)";

            // After animation completes, move first slide to end and reset position
            setTimeout(() => {
               container.style.transition = "none";
               const firstSlide = slides[0] as HTMLElement;
               container.appendChild(firstSlide);
               container.style.transform = "translateX(0)";
               isAnimatingRef.current = false;
            }, CAROUSEL_CONFIG.transitionDuration);
         } else {
            // For prev: Move last slide to beginning first, position container, then slide
            container.style.transition = "none";
            const lastSlide = slides[slides.length - 1] as HTMLElement;
            container.insertBefore(lastSlide, slides[0]);
            container.style.transform = "translateX(-100%)";

            // Then slide to show the moved element
            requestAnimationFrame(() => {
               container.style.transition = `transform ${CAROUSEL_CONFIG.transitionDuration}ms ease-in-out`;
               container.style.transform = "translateX(0)";

               // Reset after animation
               setTimeout(() => {
                  container.style.transition = "none";
                  isAnimatingRef.current = false;
               }, CAROUSEL_CONFIG.transitionDuration);
            });
         }
      },
      [transitionType],
   );

   // Navigate to next slide with DOM manipulation
   const goToNext = useCallback(() => {
      if (isTransitioning) return;

      setIsTransitioning(true);

      if (transitionType === "slide") {
         moveSlideElement("next");
      }

      setCurrentImageIndex(
         (prevIndex) => (prevIndex + 1) % HERO_BACKGROUNDS.length,
      );

      setTimeout(() => {
         setIsTransitioning(false);
      }, CAROUSEL_CONFIG.transitionDuration);
   }, [isTransitioning, transitionType, moveSlideElement]);

   // Navigate to previous slide with DOM manipulation
   const goToPrevious = useCallback(() => {
      if (isTransitioning) return;

      setIsTransitioning(true);

      if (transitionType === "slide") {
         moveSlideElement("prev");
      }

      setCurrentImageIndex((prevIndex) =>
         prevIndex === 0 ? HERO_BACKGROUNDS.length - 1 : prevIndex - 1,
      );

      setTimeout(() => {
         setIsTransitioning(false);
      }, CAROUSEL_CONFIG.transitionDuration);
   }, [isTransitioning, transitionType, moveSlideElement]);

   // Auto-advance carousel
   useEffect(() => {
      if (!isPlaying || HERO_BACKGROUNDS.length <= 1) return;

      const interval = setInterval(() => {
         goToNext();
      }, CAROUSEL_CONFIG.autoPlayInterval);

      return () => clearInterval(interval);
   }, [isPlaying, goToNext]);

   // Navigate to target slide with one-way sliding (always left)
   const navigateToSlide = useCallback(
      (targetIndex: number) => {
         if (!carouselRef.current || transitionType !== "slide") return;

         const totalSlides = HERO_BACKGROUNDS.length;

         if (targetIndex === currentImageIndex) {
            setIsTransitioning(false);
            return;
         }

         // Calculate steps for one-way sliding (always forward/left)
         let stepsToMove = targetIndex - currentImageIndex;
         if (stepsToMove <= 0) {
            stepsToMove += totalSlides; // Always go forward through the loop
         }

         // Perform multiple forward slides to reach target
         let currentStep = 0;
         const performNextSlide = () => {
            if (currentStep >= stepsToMove) {
               setIsTransitioning(false);
               return;
            }

            // Use the existing moveSlideElement for consistent animation
            moveSlideElement("next");

            // Update the logical index
            setCurrentImageIndex((prev) => (prev + 1) % totalSlides);
            currentStep++;

            // Schedule next slide if more steps needed
            if (currentStep < stepsToMove) {
               setTimeout(performNextSlide, CAROUSEL_CONFIG.transitionDuration);
            } else {
               setIsTransitioning(false);
            }
         };

         // Start the sliding sequence
         performNextSlide();
      },
      [currentImageIndex, transitionType, moveSlideElement],
   );

   // Handle manual navigation with timer reset
   const goToSlide = useCallback(
      (targetIndex: number) => {
         if (targetIndex === currentImageIndex || isTransitioning) return;

         setIsTransitioning(true);

         if (transitionType === "slide") {
            navigateToSlide(targetIndex);
         } else {
            // For fade transition, just update the index
            setCurrentImageIndex(targetIndex);
            setTimeout(() => {
               setIsTransitioning(false);
            }, CAROUSEL_CONFIG.transitionDuration);
         }

         // Reset the auto-play timer when manually navigating
         if (isPlaying && HERO_BACKGROUNDS.length > 1) {
            setIsPlaying(false);
            setTimeout(() => setIsPlaying(true), 0);
         }
      },
      [
         currentImageIndex,
         isTransitioning,
         isPlaying,
         transitionType,
         navigateToSlide,
      ],
   );

   // Keyboard navigation
   useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
         if (HERO_BACKGROUNDS.length <= 1) return;

         switch (event.key) {
            case "ArrowLeft":
               event.preventDefault();
               goToPrevious();
               break;
            case "ArrowRight":
               event.preventDefault();
               goToNext();
               break;
            case " ": // Space bar
               event.preventDefault();
               setIsPlaying((prev) => !prev);
               break;
         }
      };

      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
   }, [goToPrevious, goToNext]);

   // Pause when tab is not visible (Page Visibility API)
   useEffect(() => {
      const handleVisibilityChange = () => {
         if (document.hidden) {
            setIsPlaying(false);
         } else {
            setIsPlaying(true);
         }
      };

      document.addEventListener("visibilitychange", handleVisibilityChange);
      return () =>
         document.removeEventListener(
            "visibilitychange",
            handleVisibilityChange,
         );
   }, []);

   return (
      <section className="relative h-screen overflow-hidden">
         {/* Background Image Carousel */}
         <div className="absolute inset-0 z-0">
            {transitionType === "fade" ? (
               // Fade transition
               <>
                  {HERO_BACKGROUNDS.map((bg, index) => (
                     <div
                        key={index}
                        className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
                           index === currentImageIndex
                              ? "opacity-100"
                              : "opacity-0"
                        }`}
                     >
                        <Image
                           src={bg.src}
                           alt={bg.alt}
                           fill
                           priority={index === 0}
                           className="object-cover grayscale"
                        />
                     </div>
                  ))}
               </>
            ) : (
               // Slide transition with DOM manipulation for infinite sliding
               <div className="relative h-full w-full overflow-hidden">
                  <div ref={carouselRef} className="flex h-full">
                     {HERO_BACKGROUNDS.map((bg, index) => (
                        <div
                           key={index}
                           className="relative h-full w-full flex-shrink-0"
                        >
                           <Image
                              src={bg.src}
                              alt={bg.alt}
                              fill
                              priority={index === 0}
                              className="object-cover grayscale"
                           />
                        </div>
                     ))}
                  </div>
               </div>
            )}
         </div>

         {/* Main Content */}
         <div className="relative z-10 mx-auto flex h-full max-w-[1380px] items-center justify-start px-4 md:px-8">
            <div className="relative aspect-[16/6] w-[600px] -translate-y-10 md:w-[800px]">
               <Image
                  src="/images/hero/nominate-a-creative.png"
                  alt="Nominate a creative"
                  fill
                  priority
               />
            </div>
         </div>

         {/* Nominate Button */}
         <div className="relative mx-auto w-full max-w-[1380px] px-4 md:px-8">
            <Button
               asChild
               className="absolute bottom-35 left-0 z-20 mx-8 rounded-2xl border-2 border-gray-400 bg-black px-8 py-5 text-lg text-gray-200 shadow-md shadow-black hover:bg-gray-950 md:mx-8"
               variant="secondary"
            >
               <Link
                  href="https://docs.google.com/forms/d/e/1FAIpQLSew1e_uhrpuLbtEVDM3gwjPzqiaDmW8KNQRgKy1BIJvCq97JA/viewform?usp=dialog"
                  target="_blank"
               >
                  Nominate
               </Link>
            </Button>
         </div>

         {/* Carousel Navigation Dots */}
         {HERO_BACKGROUNDS.length > 1 && (
            <div className="absolute bottom-8 left-1/2 z-20 flex -translate-x-1/2 items-center space-x-2">
               {HERO_BACKGROUNDS.map((_, index) => {
                  const isActive = index === currentImageIndex;

                  return (
                     <button
                        key={index}
                        onClick={() => goToSlide(index)}
                        className={`border-2 border-white/50 transition-all duration-500 ease-out hover:border-white focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black/20 focus:outline-none ${
                           isActive
                              ? "h-3 w-8 scale-110 rounded-full bg-white shadow-lg"
                              : "h-3 w-3 rounded-full bg-white/30 hover:scale-110 hover:bg-white/60"
                        }`}
                        aria-label={`Go to slide ${index + 1}`}
                        aria-current={isActive ? "true" : "false"}
                        disabled={isTransitioning}
                     />
                  );
               })}

               {/* Play/Pause Indicator */}
               <div className="ml-4 flex items-center">
                  <button
                     onClick={() => setIsPlaying((prev) => !prev)}
                     className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm transition-all duration-300 hover:bg-white/30 focus:ring-2 focus:ring-white/50 focus:outline-none"
                     aria-label={isPlaying ? "Pause carousel" : "Play carousel"}
                  >
                     {isPlaying ? (
                        <div className="flex space-x-1">
                           <div className="h-3 w-1 bg-white"></div>
                           <div className="h-3 w-1 bg-white"></div>
                        </div>
                     ) : (
                        <div className="ml-0.5 h-0 w-0 border-t-[4px] border-b-[4px] border-l-[6px] border-t-transparent border-b-transparent border-l-white"></div>
                     )}
                  </button>
               </div>
            </div>
         )}

         {/* Screen Reader Only: Current slide indicator and instructions */}
         <div className="sr-only" aria-live="polite" aria-atomic="true">
            Slide {currentImageIndex + 1} of {HERO_BACKGROUNDS.length}
            {HERO_BACKGROUNDS.length > 1 && (
               <span>
                  . Use left and right arrow keys to navigate slides, or space
                  bar to pause.
               </span>
            )}
         </div>
      </section>
   );
}

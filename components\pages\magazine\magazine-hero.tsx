import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import MagazineCarousel from "./magazine-carousel";

export default function MagazineHero() {
   const currentYear = new Date().getFullYear();
   const previousYear = currentYear - 1;

   // Sample magazine data for carousel
   const pastMagazines = [
      {
         year: currentYear - 1,
         title: "Visionaries & Innovators",
         coverImage: "/images/headshots/headshot-1.png",
         description:
            "Featuring the year's most exceptional creatives who redefined their industries.",
      },
      {
         year: currentYear - 2,
         title: "Creative Pioneers",
         coverImage: "/images/headshots/headshot-2.png",
         description:
            "Celebrating breakthrough artists and designers who shaped the creative landscape.",
      },
      {
         year: currentYear - 3,
         title: "Digital Revolutionaries",
         coverImage: "/images/headshots/headshot-3.png",
         description:
            "Honoring the digital creators who transformed how we experience art and design.",
      },
   ];

   return (
      <section className="relative h-screen overflow-hidden">
         {/* Background Image */}
         <div className="absolute inset-0 z-0">
            <Image
               src="/images/hero/hero-bg.png"
               alt="Magazine background"
               fill
               priority
               className="object-cover"
            />
         </div>

         {/* Hero Content */}
         <div className="relative z-10 mx-auto flex h-full max-w-[1380px] items-center px-4 md:px-8">
            <div className="grid w-full grid-cols-1 gap-8 lg:grid-cols-2 lg:gap-16">
               {/* Left Column - Text Content */}
               <div className="flex flex-col justify-center">
                  {/* Main Hero Text */}
                  <h1 className="text-5xl font-bold text-white md:text-6xl lg:text-7xl">
                     {previousYear}
                  </h1>
                  <h2 className="mt-4 text-3xl font-bold text-white md:text-4xl lg:text-5xl">
                     Annual Collection
                  </h2>
                  <p className="text-md mt-6 text-white/90 md:text-lg lg:text-xl">
                     Featuring the year&apos;s most exceptional creatives who
                     redefined their industries and inspired a new generation of
                     talent.
                  </p>

                  {/* Download Button */}
                  <div className="mt-8">
                     <Button
                        asChild
                        className="rounded-2xl border-2 border-gray-400 bg-black px-8 py-6 text-lg text-gray-200 shadow-md shadow-black hover:bg-gray-950"
                        variant="secondary"
                        aria-label={`Download Mlist Magazine ${previousYear}`}
                     >
                        <Link href="#">Download {previousYear} Edition</Link>
                     </Button>
                  </div>

                  {/* Additional Info */}
                  <div className="mt-8 text-white/70">
                     <p className="text-sm md:text-base">
                        Explore our archive of past editions and discover the
                        stories that shaped the creative industry.
                     </p>
                  </div>
               </div>

               {/* Right Column - Magazine Carousel */}
               <div className="hidden items-center justify-center lg:flex lg:justify-end">
                  <div className="w-full max-w-sm">
                     <MagazineCarousel magazines={pastMagazines} />
                  </div>
               </div>
            </div>
         </div>
      </section>
   );
}

import MagazineItem from "./magazine-item";

export default function MagazineArchive() {
   const currentYear = new Date().getFullYear();

   const magazines = [
      {
         year: currentYear - 1, // 2023
         title: "Visionaries & Innovators",
         coverImage: "/images/hero/hero-bg.png",
         description:
            "Featuring the year's most exceptional creatives who redefined their industries and inspired a new generation of talent. From groundbreaking designers to revolutionary artists, this edition celebrates those who dared to think differently.",
      },
      {
         year: currentYear - 2, // 2022
         title: "Creative Pioneers",
         coverImage: "/images/hero/hero-bg.png",
         description:
            "Celebrating breakthrough artists and designers who shaped the creative landscape. This collection showcases the minds behind the most influential projects that transformed how we see and interact with the world around us.",
      },
      {
         year: currentYear - 3, // 2021
         title: "Digital Revolutionaries",
         coverImage: "/images/hero/hero-bg.png",
         description:
            "Honoring the digital creators who transformed how we experience art and design. From interactive installations to cutting-edge digital art, these creators pushed the boundaries of what's possible in the digital realm.",
      },
   ];

   return (
      <section className="py-20">
         <div className="mx-auto max-w-6xl px-4 md:px-8">
            {/* Section Header */}
            <div className="mb-16 text-center">
               <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
                  Magazine Archive
               </h2>
               <p className="mx-auto max-w-3xl text-lg text-gray-600">
                  Explore our collection of annual magazines featuring the most
                  exceptional creatives who have shaped their industries and
                  inspired countless others.
               </p>
            </div>

            {/* Magazine List - Enhanced Vertical Layout */}
            <div className="space-y-2">
               {magazines.map((magazine) => (
                  <MagazineItem
                     key={magazine.year}
                     year={magazine.year}
                     title={magazine.title}
                     coverImage={magazine.coverImage}
                     description={magazine.description}
                  />
               ))}
            </div>

            {/* Enhanced Newsletter CTA */}
            <div className="mt-8 rounded-3xl bg-gradient-to-r from-gray-900 to-black p-12 text-center text-white">
               <div className="mx-auto max-w-2xl">
                  <h3 className="mb-4 text-3xl font-bold md:text-4xl">
                     Stay Updated with Mlist
                  </h3>
                  <p className="mb-8 text-lg text-white/90">
                     Subscribe to our newsletter to receive the latest
                     magazines, exclusive content, and updates about featured
                     creatives.
                  </p>
                  <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
                     <a
                        href="/newsletter"
                        className="inline-flex items-center rounded-2xl bg-white px-6 py-3 text-base font-medium text-black shadow-lg transition-all duration-300 hover:bg-gray-100"
                        aria-label="Subscribe to MLIST newsletter"
                     >
                        <svg
                           className="mr-2 h-5 w-5"
                           fill="currentColor"
                           viewBox="0 0 20 20"
                        >
                           <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                           <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                        Join Our Newsletter
                     </a>
                  </div>
               </div>
            </div>
         </div>
      </section>
   );
}

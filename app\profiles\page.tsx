import { profilesList } from "@/data/sample-data";
import type { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const metadata: Metadata = {
   title: "Profiles | Mlist",
   description: "Explore profiles of exceptional creatives featured on Mlist.",
};

export default function ProfilesPage() {
   return (
      <main className="min-h-screen">
         <div className="relative mt-[80px] bg-gray-100 py-14 md:mt-[100px]">
            <div className="mx-auto max-w-[1380px] px-4 md:px-8">
               <h1 className="text-2xl font-bold md:text-3xl">Profiles</h1>
               <p className="text-md mt-4 text-gray-600">
                  Explore the profiles of exceptional creatives who have been
                  featured on Mlist. Learn about their stories, achievements,
                  and creative journeys.
               </p>
            </div>
         </div>

         <div className="mx-auto max-w-[1380px] px-4 py-12 md:px-8">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
               {profilesList.map((profile) => (
                  <Link
                     key={`${profile.slug}-${profile.userId}`}
                     href={`/profiles/${profile.slug}`}
                     className="group overflow-hidden"
                  >
                     <div className="relative aspect-square overflow-hidden">
                        <Image
                           src={profile.image}
                           alt={profile.name}
                           fill
                           className="object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                     </div>
                     <div className="mt-4">
                        <h2 className="text-lg font-semibold">
                           {profile.name}
                        </h2>
                        <p className="text-md text-gray-600">
                           {profile.occupation}
                        </p>
                        <p className="mt-2 line-clamp-2 text-gray-500">
                           {profile.description}
                        </p>
                     </div>
                  </Link>
               ))}
            </div>
         </div>
      </main>
   );
}

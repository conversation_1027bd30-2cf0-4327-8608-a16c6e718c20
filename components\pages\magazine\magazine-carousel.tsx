"use client";

import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useState } from "react";

interface Magazine {
   year: number;
   title: string;
   coverImage: string;
   description: string;
}

interface MagazineCarouselProps {
   magazines: Magazine[];
}

export default function MagazineCarousel({ magazines }: MagazineCarouselProps) {
   const [currentIndex, setCurrentIndex] = useState(0);
   const [isAutoPlaying, setIsAutoPlaying] = useState(true);

   // Auto-play functionality
   useEffect(() => {
      if (!isAutoPlaying || magazines.length <= 1) return;

      const interval = setInterval(() => {
         setCurrentIndex((prevIndex) =>
            prevIndex === magazines.length - 1 ? 0 : prevIndex + 1,
         );
      }, 4000);

      return () => clearInterval(interval);
   }, [isAutoPlaying, magazines.length]);

   const goToPrevious = useCallback(() => {
      setIsAutoPlaying(false);
      setCurrentIndex(
         currentIndex === 0 ? magazines.length - 1 : currentIndex - 1,
      );
   }, [currentIndex, magazines.length]);

   const goToNext = useCallback(() => {
      setIsAutoPlaying(false);
      setCurrentIndex(
         currentIndex === magazines.length - 1 ? 0 : currentIndex + 1,
      );
   }, [currentIndex, magazines.length]);

   const goToSlide = (index: number) => {
      setIsAutoPlaying(false);
      setCurrentIndex(index);
   };

   // Handle keyboard navigation
   useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
         if (e.key === "ArrowLeft") {
            goToPrevious();
         } else if (e.key === "ArrowRight") {
            goToNext();
         }
      };

      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
   }, [currentIndex, goToNext, goToPrevious]);

   if (magazines.length === 0) return null;

   return (
      <div
         className="relative w-full max-w-md"
         onMouseEnter={() => setIsAutoPlaying(false)}
         onMouseLeave={() => setIsAutoPlaying(true)}
         role="region"
         aria-label="Magazine preview carousel"
      >
         {/* Main Carousel Container */}
         <div className="relative aspect-[3/4] overflow-hidden shadow-2xl">
            {magazines.map((magazine, index) => (
               <div
                  key={magazine.year}
                  className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
                     index === currentIndex
                        ? "translate-x-0"
                        : index < currentIndex
                          ? "-translate-x-full"
                          : "translate-x-full"
                  }`}
               >
                  <Image
                     src={magazine.coverImage}
                     alt={`Mlist Magazine ${magazine.year} cover`}
                     fill
                     className="object-cover"
                     priority={index === 0}
                  />
                  {/* Magazine Info Overlay */}
                  <div className="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black via-black/60 to-transparent p-6">
                     {/* <p className="mb-1 text-sm font-medium text-white/90">
                        {magazine.year} Edition
                     </p> */}
                     <div className="mb-2 w-fit rounded-sm bg-white/90 px-4 py-1 shadow-lg backdrop-blur-sm">
                        <p className="text-sm font-semibold text-gray-900">
                           {magazine.year} Edition
                        </p>
                     </div>
                     <h3 className="line-clamp-2 text-lg font-bold text-white">
                        {magazine.title}
                     </h3>
                  </div>
               </div>
            ))}
         </div>

         {/* Navigation Arrows */}
         {magazines.length > 1 && (
            <>
               <Button
                  onClick={goToPrevious}
                  className="absolute top-1/2 left-2 h-10 w-10 -translate-y-1/2 rounded-md bg-white/20 p-0 text-white backdrop-blur-sm hover:cursor-pointer hover:bg-white/30 focus:bg-white/30"
                  aria-label="Previous magazine"
               >
                  <ChevronLeft size={20} />
               </Button>
               <Button
                  onClick={goToNext}
                  className="absolute top-1/2 right-2 h-10 w-10 -translate-y-1/2 rounded-md bg-white/20 p-0 text-white backdrop-blur-sm hover:cursor-pointer hover:bg-white/30 focus:bg-white/30"
                  aria-label="Next magazine"
               >
                  <ChevronRight size={20} />
               </Button>
            </>
         )}

         {/* Dot Indicators */}
         {magazines.length > 1 && (
            <div className="absolute -bottom-8 left-1/2 flex -translate-x-1/2 space-x-2">
               {magazines.map((_, index) => (
                  <button
                     key={index}
                     onClick={() => goToSlide(index)}
                     className={`h-2 w-2 rounded-full transition-all duration-200 ${
                        index === currentIndex
                           ? "w-6 bg-white"
                           : "bg-white/50 hover:bg-white/70"
                     }`}
                     aria-label={`Go to magazine ${magazines[index].year}`}
                  />
               ))}
            </div>
         )}
      </div>
   );
}
